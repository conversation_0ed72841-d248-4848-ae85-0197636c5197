# Sacred Breath - Modular CSS Architecture

This directory contains the modular CSS architecture for the Sacred Breath application. The original monolithic `styles.css` file has been refactored into smaller, maintainable modules.

## File Structure

```
css/
├── main.css          # Main entry point - imports all modules
├── base.css          # Foundation styles and variables
├── animations.css    # All keyframe animations
├── background.css    # Cosmic background effects
├── layout.css        # Layout and typography
├── moon.css          # Moon portal and chakra styles
├── controls.css      # Controls and wisdom panel
├── responsive.css    # Responsive design
└── README.md         # This file
```

## Module Descriptions

### 1. `main.css` - Entry Point
- Imports all other CSS modules in correct order
- Contains documentation about the architecture
- Single file to include in HTML

### 2. `base.css` - Foundation (47 lines)
- CSS reset and normalization
- CSS custom properties (color palette, typography, geometry)
- Basic body and html styles
- Core cosmic background gradient

### 3. `animations.css` - Animations (158 lines)
- All `@keyframes` definitions
- Cosmic background animations (stars, nebula, aurora)
- Moon portal animations (energy pulses, inner light)
- Button and UI animations
- Breathing wave animations
- Wisdom panel animations

### 4. `background.css` - Background Effects (118 lines)
- Cosmic background container
- Stars field with multiple gradients
- Nebula effects with flowing animations
- Aurora shimmer effects
- Cosmic energy waves
- Wave phase animations (inhale/hold/exhale)

### 5. `layout.css` - Layout & Typography (170 lines)
- App container and main structure
- Header with sacred symbols and title
- Instruction container and phase indicators
- Timer and cycle tracker
- Footer layout
- Typography and text effects
- Phase-specific styling

### 6. `moon.css` - Moon Portal System (245 lines)
- Moon portal container and positioning
- Energy field and pulse effects
- Moon core, inner light, and runes
- Moon aura effects
- Chakra points positioning and styling
- Moon phase transformations (new, full, waning)
- Exhale-specific energy pulse animations

### 7. `controls.css` - Interactive Elements (444 lines)
- Mystical control buttons
- Button states and hover effects
- Phase-specific button styling
- Simple control buttons (start/stop)
- Wisdom panel (collapsible)
- Wisdom panel content and animations
- Button rotation and pulse effects

### 8. `responsive.css` - Mobile Optimization (320 lines)
- Tablet breakpoints (768px and below)
- Mobile breakpoints (480px and below)
- Short screen optimizations (700px height and below)
- Very short screen handling (500px height and below)
- Mobile browser safe area handling
- Touch-friendly button sizing

## Benefits of Modular Architecture

### Maintainability
- Each module has a single responsibility
- Easy to locate and modify specific features
- Reduced risk of unintended side effects

### Performance
- Smaller individual files for faster loading
- Better browser caching (unchanged modules stay cached)
- Easier to identify and optimize performance bottlenecks

### Development Experience
- Cleaner code organization
- Easier debugging and testing
- Better collaboration (multiple developers can work on different modules)

### Scalability
- Easy to add new features as separate modules
- Simple to remove unused features
- Modular imports allow for custom builds

## File Size Comparison

| File | Lines | Purpose |
|------|-------|---------|
| Original `styles.css` | 1,705 | Monolithic file |
| **New Modular Total** | **1,502** | **All modules combined** |
| `main.css` | 47 | Import and documentation |
| `base.css` | 47 | Foundation |
| `animations.css` | 158 | All animations |
| `background.css` | 118 | Background effects |
| `layout.css` | 170 | Layout and typography |
| `moon.css` | 245 | Moon portal system |
| `controls.css` | 444 | Controls and panels |
| `responsive.css` | 320 | Responsive design |

**Total reduction: 203 lines (12% smaller)**

## Usage

### In HTML
```html
<link rel="stylesheet" href="css/main.css">
```

### For Custom Builds
You can import only the modules you need:
```css
@import url('./base.css');
@import url('./animations.css');
@import url('./moon.css');
/* Skip other modules for lighter builds */
```

## Development Guidelines

### Adding New Features
1. Determine which module the feature belongs to
2. If it doesn't fit existing modules, create a new one
3. Update `main.css` to import the new module
4. Update this README

### Modifying Existing Features
1. Locate the appropriate module
2. Make changes within that module only
3. Test to ensure no cross-module dependencies are broken

### CSS Custom Properties
- All color variables are defined in `base.css`
- Use existing variables when possible
- Add new variables to `base.css` if needed

## Browser Support
- Modern browsers with CSS Grid and Flexbox support
- CSS custom properties (variables) support required
- CSS `@import` support required
