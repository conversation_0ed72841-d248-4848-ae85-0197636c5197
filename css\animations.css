/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - ANIMATIONS
   All keyframe animations and transitions
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* ═══ COSMIC ANIMATIONS ═══ */
@keyframes cosmicShift {
    0%, 100% { filter: hue-rotate(0deg) brightness(1); }
    33% { filter: hue-rotate(120deg) brightness(1.1); }
    66% { filter: hue-rotate(240deg) brightness(0.9); }
}

@keyframes starsTwinkle {
    0% { opacity: 0.4; transform: scale(1); }
    25% { opacity: 0.8; transform: scale(1.1); }
    50% { opacity: 0.6; transform: scale(0.9); }
    75% { opacity: 1; transform: scale(1.05); }
    100% { opacity: 0.5; transform: scale(1); }
}

@keyframes starsFloat {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(5px) translateY(-10px); }
    75% { transform: translateX(-5px) translateY(5px); }
    100% { transform: translateX(0) translateY(0); }
}

@keyframes nebulaFlow {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    33% { transform: rotate(3deg) scale(1.03); opacity: 1; }
    66% { transform: rotate(-2deg) scale(1.07); opacity: 0.9; }
}

@keyframes nebulaDrift {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    25% { transform: translateX(20px) translateY(-15px) rotate(2deg); }
    50% { transform: translateX(-10px) translateY(10px) rotate(-1deg); }
    75% { transform: translateX(15px) translateY(20px) rotate(1deg); }
    100% { transform: translateX(0) translateY(0) rotate(0deg); }
}

@keyframes auroraShimmer {
    0%, 100% { opacity: 0.3; transform: translateY(0px); }
    50% { opacity: 0.7; transform: translateY(-10px); }
}

/* ═══ HEADER ANIMATIONS ═══ */
@keyframes sacredPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 1; }
}

@keyframes titleGlow {
    from { text-shadow: 0 0 30px var(--mystical-violet), 0 0 60px var(--ethereal-cyan); }
    to { text-shadow: 0 0 40px var(--celestial-pink), 0 0 80px var(--sacred-gold); }
}

@keyframes runePulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; text-shadow: 0 0 15px var(--sacred-gold); }
}

/* ═══ MOON ANIMATIONS ═══ */
@keyframes energyPulse {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
    25% { opacity: 0.3; transform: translate(-50%, -50%) scale(1.0); }
    50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
    75% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.05); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
}

@keyframes innerLightPulse {
    0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes runesGlow {
    from { text-shadow: 0 0 20px var(--mystical-violet); }
    to { text-shadow: 0 0 30px var(--ethereal-cyan), 0 0 40px var(--sacred-gold); }
}

@keyframes auraShimmer {
    0%, 100% { opacity: 0.2; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes chakraPulse {
    0%, 100% { opacity: 0.6; transform: translateX(-50%) scale(1); }
    50% { opacity: 1; transform: translateX(-50%) scale(1.3); }
}

@keyframes energyPulseExhale {
    0% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(0.9);
        border-width: 2px;
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.8);
        border-width: 1px;
    }
}

/* ═══ WAVE ANIMATIONS ═══ */
/* Inhale - Contracting waves from screen edges */
@keyframes cosmicWaveInhale {
    0% { width: 120vmax; height: 120vmax; opacity: 0.2; transform: translate(-50%, -50%) scale(1.0); }
    30% { width: 80vmax; height: 80vmax; opacity: 0.4; transform: translate(-50%, -50%) scale(0.95); }
    70% { width: 400px; height: 400px; opacity: 0.6; transform: translate(-50%, -50%) scale(0.9); }
    100% { width: 280px; height: 280px; opacity: 0.3; transform: translate(-50%, -50%) scale(0.85); }
}

/* Hold - Slow pulsing */
@keyframes cosmicWaveHold {
    0%, 100% { width: 250px; height: 250px; opacity: 0.4; transform: translate(-50%, -50%) scale(0.9); }
    50% { width: 280px; height: 280px; opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
}

/* Exhale - Linear expanding waves matching 8s breath rhythm */
@keyframes cosmicWaveExhale {
    0% {
        width: 280px;
        height: 280px;
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(0.9);
    }
    100% {
        width: 120vmax;
        height: 120vmax;
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.4);
    }
}

/* ═══ INSTRUCTION ANIMATIONS ═══ */
@keyframes phaseShimmer {
    from { filter: brightness(1) saturate(1); }
    to { filter: brightness(1.2) saturate(1.5); }
}

@keyframes symbolRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes timerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ═══ BUTTON ANIMATIONS ═══ */
@keyframes btnRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes symbolPulse {
    0%, 100% { transform: scale(1); text-shadow: 0 0 20px currentColor; }
    50% { transform: scale(1.1); text-shadow: 0 0 35px currentColor, 0 0 50px var(--sacred-gold); }
}

@keyframes runeShimmer {
    0%, 100% { opacity: 0.8; text-shadow: 0 0 10px var(--sacred-gold); }
    50% { opacity: 1; text-shadow: 0 0 20px var(--sacred-gold), 0 0 30px var(--ethereal-cyan); }
}

@keyframes activeButtonPulse {
    0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 0 50px rgba(236, 72, 153, 0.5), inset 0 0 35px rgba(236, 72, 153, 0.2); 
    }
    50% { 
        transform: scale(1.05); 
        box-shadow: 0 0 70px rgba(236, 72, 153, 0.7), inset 0 0 40px rgba(236, 72, 153, 0.3); 
    }
}

/* ═══ WISDOM PANEL ANIMATIONS ═══ */
@keyframes mysticalPulse {
    0%, 100% { 
        opacity: 0.8; 
        transform: translate(-50%, -50%) scale(1) rotate(0deg); 
        text-shadow: 0 0 10px var(--sacred-gold), 0 0 20px var(--ethereal-cyan); 
    }
    50% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg); 
        text-shadow: 0 0 15px var(--sacred-gold), 0 0 30px var(--mystical-violet); 
    }
}

@keyframes ancientPulse {
    from { opacity: 0.7; }
    to { opacity: 1; text-shadow: 0 0 20px var(--sacred-gold); }
}
