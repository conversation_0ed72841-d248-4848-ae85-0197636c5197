/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - MYSTICAL CSS ALCHEMY
   A cosmic journey through lunar breathing meditation
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* Universal Reset & Cosmic Foundation */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

:root {
    /* Mystical Color Palette */
    --cosmic-void: #000511;
    --deep-space: #0a0a1a;
    --nebula-purple: #2d1b69;
    --astral-blue: #1e3a8a;
    --ethereal-cyan: #06b6d4;
    --lunar-silver: #e2e8f0;
    --sacred-gold: #fbbf24;
    --mystical-violet: #8b5cf6;
    --aurora-green: #10b981;
    --celestial-pink: #ec4899;

    /* Sacred Geometry */
    --golden-ratio: 1.618;
    --sacred-timing: 0.618s;

    /* Mystical Typography */
    --font-mystical: '<PERSON><PERSON><PERSON>', serif;
    --font-ethereal: 'Philosopher', sans-serif;
}

body,
html {
    height: 100%;
    min-height: 100vh;
    overflow: hidden;
    font-family: var(--font-ethereal);
    background: var(--cosmic-void);
    color: var(--lunar-silver);
    cursor: default;
}

body {
    background:
        radial-gradient(circle at 20% 80%, var(--nebula-purple) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, var(--astral-blue) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--deep-space) 0%, transparent 70%),
        var(--cosmic-void);
    animation: cosmicShift 60s ease-in-out infinite;
}

@keyframes cosmicShift {
    0%, 100% { filter: hue-rotate(0deg) brightness(1); }
    33% { filter: hue-rotate(120deg) brightness(1.1); }
    66% { filter: hue-rotate(240deg) brightness(0.9); }
}

/* ═══ COSMIC BACKGROUND ═══ */
.cosmic-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1;
    pointer-events: none;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(2px 2px at 20px 30px, #06b6d4, transparent),
        radial-gradient(2px 2px at 40px 70px, #e2e8f0, transparent),
        radial-gradient(1px 1px at 90px 40px, #fbbf24, transparent),
        radial-gradient(1px 1px at 130px 80px, #8b5cf6, transparent),
        radial-gradient(2px 2px at 160px 30px, #ec4899, transparent),
        radial-gradient(1px 1px at 180px 60px, #10b981, transparent),
        radial-gradient(1.5px 1.5px at 50px 90px, #06b6d4, transparent),
        radial-gradient(1px 1px at 170px 10px, #e2e8f0, transparent);
    background-repeat: repeat;
    background-size: 200px 120px;
    animation: starsTwinkle 3s ease-in-out infinite alternate, starsFloat 20s linear infinite;
}

@keyframes starsTwinkle {
    0% { opacity: 0.4; transform: scale(1); }
    25% { opacity: 0.8; transform: scale(1.1); }
    50% { opacity: 0.6; transform: scale(0.9); }
    75% { opacity: 1; transform: scale(1.05); }
    100% { opacity: 0.5; transform: scale(1); }
}

@keyframes starsFloat {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(5px) translateY(-10px); }
    75% { transform: translateX(-5px) translateY(5px); }
    100% { transform: translateX(0) translateY(0); }
}

.nebula {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 80% 50% at 30% 70%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
        radial-gradient(ellipse 60% 40% at 70% 30%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse 90% 30% at 50% 90%, rgba(236, 72, 153, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse 70% 60% at 20% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 60%),
        radial-gradient(ellipse 50% 80% at 80% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 70%);
    animation: nebulaFlow 45s ease-in-out infinite, nebulaDrift 90s linear infinite;
}

@keyframes nebulaFlow {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    33% { transform: rotate(3deg) scale(1.03); opacity: 1; }
    66% { transform: rotate(-2deg) scale(1.07); opacity: 0.9; }
}

@keyframes nebulaDrift {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    25% { transform: translateX(20px) translateY(-15px) rotate(2deg); }
    50% { transform: translateX(-10px) translateY(10px) rotate(-1deg); }
    75% { transform: translateX(15px) translateY(20px) rotate(1deg); }
    100% { transform: translateX(0) translateY(0) rotate(0deg); }
}

.aurora {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg,
            transparent 30%,
            rgba(16, 185, 129, 0.05) 50%,
            transparent 70%),
        linear-gradient(-45deg,
            transparent 40%,
            rgba(251, 191, 36, 0.04) 60%,
            transparent 80%);
    animation: auroraShimmer 30s ease-in-out infinite;
}

@keyframes auroraShimmer {
    0%, 100% { opacity: 0.3; transform: translateY(0px); }
    50% { opacity: 0.7; transform: translateY(-10px); }
}

/* App container - responsive fullscreen */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 15px 10px;
    overflow: hidden;
}

/* ═══ MYSTICAL HEADER ═══ */
.header {
    text-align: center;
    z-index: 10;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    max-width: 800px;
    margin-bottom: 80px;
}

.sacred-symbol.top-symbol {
    font-size: 2.5rem;
    color: var(--sacred-gold);
    text-shadow: 0 0 20px var(--sacred-gold);
    margin-bottom: 10px;
    display: block;
    animation: sacredPulse 4s ease-in-out infinite;
}

@keyframes sacredPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 1; }
}

.title {
    font-family: var(--font-mystical);
    font-size: 2.2rem;
    font-weight: 400;
    margin-bottom: 10px;
    color: var(--lunar-silver);
    text-shadow:
        0 0 20px var(--mystical-violet),
        0 0 40px var(--ethereal-cyan);
    letter-spacing: 2px;
    animation: titleGlow 6s ease-in-out infinite alternate;
    line-height: 1.2;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 30px var(--mystical-violet), 0 0 60px var(--ethereal-cyan); }
    to { text-shadow: 0 0 40px var(--celestial-pink), 0 0 80px var(--sacred-gold); }
}

.subtitle-container {
    margin-top: 8px;
}

.mystical-text {
    font-family: var(--font-ethereal);
    font-size: 1rem;
    color: var(--ethereal-cyan);
    font-style: italic;
    text-shadow: 0 0 15px var(--ethereal-cyan);
    display: block;
    margin-bottom: 5px;
}

.rune-divider {
    font-size: 0.9rem;
    color: var(--sacred-gold);
    opacity: 0.7;
    letter-spacing: 4px;
    animation: runePulse 8s ease-in-out infinite;
}

@keyframes runePulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; text-shadow: 0 0 15px var(--sacred-gold); }
}

/* ═══ MAIN MYSTICAL CONTAINER ═══ */
.breathing-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 600px;
}

/* ═══ LUNAR PORTAL ═══ */
.moon-container {
    position: relative;
    margin-bottom: 10px;
    z-index: 5;
    flex-shrink: 0;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 380px;
    height: 380px;
    z-index: 1;
}

.energy-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.pulse-1 {
    width: 350px;
    height: 350px;
    border-color: var(--mystical-violet);
    animation: energyPulse 4s linear infinite;
}

.pulse-2 {
    width: 300px;
    height: 300px;
    border-color: var(--ethereal-cyan);
    animation: energyPulse 4s linear infinite 1.3s;
}

.pulse-3 {
    width: 250px;
    height: 250px;
    border-color: var(--sacred-gold);
    animation: energyPulse 4s linear infinite 2.6s;
}

@keyframes energyPulse {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
    25% { opacity: 0.3; transform: translate(-50%, -50%) scale(1.0); }
    50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
    75% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.05); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
}

.moon-portal {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    position: relative;
    z-index: 3;
    background: var(--lunar-silver);
    box-shadow:
        0 0 80px var(--mystical-violet),
        0 0 120px rgba(139, 92, 246, 0.3),
        inset 0 0 50px var(--deep-space);
    transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.moon-portal:hover {
    transform: scale(1.02);
    box-shadow:
        0 0 90px var(--mystical-violet),
        0 0 140px rgba(139, 92, 246, 0.4),
        inset 0 0 55px var(--deep-space);
}

.moon-portal:active {
    transform: scale(0.98);
    transition: all 0.1s ease;
}

.moon-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, var(--lunar-silver), var(--deep-space));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    overflow: hidden;
}

.moon-inner-light {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--sacred-gold) 0%, transparent 70%);
    opacity: 0.6;
    animation: innerLightPulse 8s ease-in-out infinite;
}

@keyframes innerLightPulse {
    0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

.moon-runes {
    font-size: 2.2rem;
    color: var(--mystical-violet);
    text-shadow: 0 0 25px var(--mystical-violet);
    z-index: 2;
    letter-spacing: 10px;
    animation: runesGlow 6s ease-in-out infinite alternate;
}

@keyframes runesGlow {
    from { text-shadow: 0 0 20px var(--mystical-violet); }
    to { text-shadow: 0 0 30px var(--ethereal-cyan), 0 0 40px var(--sacred-gold); }
}

.moon-aura {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 380px;
    height: 380px;
    border-radius: 50%;
    background: radial-gradient(circle, transparent 40%, var(--mystical-violet) 70%, transparent 100%);
    opacity: 0.3;
    animation: auraShimmer 10s ease-in-out infinite;
    z-index: 0;
}

@keyframes auraShimmer {
    0%, 100% { opacity: 0.2; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.1); }
}

/* ═══ CHAKRA POINTS ═══ */
.chakra-points {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 420px;
    height: 420px;
    z-index: 4;
}

.chakra {
    position: absolute;
    width: 25px;
    height: 25px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-shadow: 0 0 15px currentColor;
    animation: chakraPulse 8s ease-in-out infinite;
}

.chakra-1 {
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: var(--celestial-pink);
    animation-delay: 0s;
}

.chakra-2 {
    top: 50%;
    right: 10%;
    transform: translateY(-50%);
    color: var(--sacred-gold);
    animation-delay: 2s;
}

.chakra-3 {
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: var(--aurora-green);
    animation-delay: 4s;
}

.chakra-4 {
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    color: var(--ethereal-cyan);
    animation-delay: 6s;
}

@keyframes chakraPulse {
    0%, 100% { opacity: 0.6; transform: translateX(-50%) scale(1); }
    50% { opacity: 1; transform: translateX(-50%) scale(1.3); }
}

/* ═══ MOON PHASE TRANSFORMATIONS ═══ */
.moon-portal.new-moon {
    background: radial-gradient(circle at 30% 30%, var(--deep-space), var(--cosmic-void));
    box-shadow:
        0 0 80px var(--mystical-violet),
        0 0 120px rgba(139, 92, 246, 0.4),
        inset 0 0 80px var(--mystical-violet);
    transform: scale(0.9);
}

.moon-portal.new-moon .moon-core {
    background: radial-gradient(circle at 30% 30%, var(--deep-space), var(--cosmic-void));
}

.moon-portal.new-moon .moon-inner-light {
    background: radial-gradient(circle, var(--mystical-violet) 0%, transparent 60%);
    opacity: 0.8;
}

.moon-portal.full-moon {
    background: radial-gradient(circle at 30% 30%, var(--lunar-silver), #f8fafc);
    box-shadow:
        0 0 120px var(--sacred-gold),
        0 0 180px var(--ethereal-cyan),
        0 0 240px rgba(251, 191, 36, 0.3),
        inset 0 0 60px var(--mystical-violet);
    transform: scale(1.1);
}

.moon-portal.full-moon .moon-core {
    background: radial-gradient(circle at 30% 30%, #ffffff, var(--lunar-silver));
}

.moon-portal.full-moon .moon-inner-light {
    background: radial-gradient(circle, var(--sacred-gold) 0%, transparent 80%);
    opacity: 1;
    animation-duration: 4s;
}

.moon-portal.waning-moon {
    background: linear-gradient(90deg, var(--cosmic-void) 45%, var(--lunar-silver) 55%);
    box-shadow:
        0 0 100px var(--celestial-pink),
        0 0 140px rgba(236, 72, 153, 0.4),
        inset 0 0 70px var(--aurora-green);
    transform: scale(0.95);
}

.moon-portal.waning-moon .moon-core {
    background: linear-gradient(90deg, var(--deep-space) 45%, var(--lunar-silver) 55%);
}

.moon-portal.waning-moon .moon-inner-light {
    background: radial-gradient(circle, var(--celestial-pink) 0%, transparent 70%);
    opacity: 0.7;
}

/* ═══ COSMIC ENERGY WAVES ═══ */
.cosmic-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 1;
}

.cosmic-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 4px solid;
    opacity: 0;
    transform: translate(-50%, -50%);
}

.wave-ethereal {
    border-color: var(--ethereal-cyan);
    box-shadow:
        0 0 30px var(--ethereal-cyan),
        inset 0 0 20px rgba(6, 182, 212, 0.1);
    animation-delay: 0s;
}

.wave-astral {
    border-color: var(--mystical-violet);
    box-shadow:
        0 0 25px var(--mystical-violet),
        inset 0 0 15px rgba(139, 92, 246, 0.1);
    animation-delay: 1.2s;
}

.wave-spiritual {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 35px var(--sacred-gold),
        inset 0 0 25px rgba(251, 191, 36, 0.1);
    animation-delay: 2.4s;
}

.wave-divine {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 28px var(--celestial-pink),
        inset 0 0 18px rgba(236, 72, 153, 0.1);
    animation-delay: 3.6s;
}

.wave-cosmic {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 32px var(--aurora-green),
        inset 0 0 22px rgba(16, 185, 129, 0.1);
    animation-delay: 4.8s;
}

/* Inhale - Contracting waves from screen edges */
@keyframes cosmicWaveInhale {
    0% {
        width: 120vmax;
        height: 120vmax;
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1.0);
    }

    30% {
        width: 80vmax;
        height: 80vmax;
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(0.95);
    }

    70% {
        width: 400px;
        height: 400px;
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(0.9);
    }

    100% {
        width: 280px;
        height: 280px;
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(0.85);
    }
}

/* Hold - Slow pulsing */
@keyframes cosmicWaveHold {

    0%,
    100% {
        width: 250px;
        height: 250px;
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(0.9);
    }

    50% {
        width: 280px;
        height: 280px;
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Exhale - Expanding waves */
@keyframes cosmicWaveExhale {
    0% {
        width: 280px;
        height: 280px;
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(0.85);
    }

    25% {
        width: 50vmax;
        height: 50vmax;
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(0.95);
    }

    60% {
        width: 90vmax;
        height: 90vmax;
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1.1);
    }

    100% {
        width: 130vmax;
        height: 130vmax;
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.3);
    }
}

/* Phase-specific wave animations */
.cosmic-wave.active.phase-inhale {
    animation: cosmicWaveInhale 4s linear infinite;
}

.cosmic-wave.active.phase-hold {
    animation: cosmicWaveHold 7s ease-in-out infinite;
}

.cosmic-wave.active.phase-exhale {
    animation: cosmicWaveExhale 8s linear infinite;
}

/* Default state */
.cosmic-wave.active {
    animation: cosmicWaveHold 8s ease-in-out infinite;
}

/* ═══ MYSTICAL INSTRUCTIONS ═══ */
.instruction-container {
    text-align: center;
    z-index: 10;
    position: relative;
    flex-shrink: 0;
    width: 100%;
}

.mystical-phase {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
    animation: phaseShimmer 8s ease-in-out infinite alternate;
}

@keyframes phaseShimmer {
    from {
        filter: brightness(1) saturate(1);
    }

    to {
        filter: brightness(1.2) saturate(1.5);
    }
}

.phase-symbol {
    font-size: 2.5rem;
    margin-bottom: 8px;
    text-shadow: 0 0 20px currentColor;
    animation: symbolRotate 20s linear infinite;
}

@keyframes symbolRotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.phase-text {
    font-family: var(--font-mystical);
    font-size: 1.3rem;
    font-weight: 400;
    color: var(--lunar-silver);
    text-shadow: 0 0 15px currentColor;
    letter-spacing: 1px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 1s ease;
}

.cosmic-timer {
    font-family: var(--font-mystical);
    font-size: 3rem;
    font-weight: 300;
    color: var(--ethereal-cyan);
    text-shadow:
        0 0 20px var(--ethereal-cyan),
        0 0 40px var(--mystical-violet);
    margin-bottom: 10px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: timerPulse 4s ease-in-out infinite;
}

@keyframes timerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.cycle-tracker {
    font-family: var(--font-ethereal);
    font-size: 1.3rem;
    color: var(--sacred-gold);
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.rune-counter {
    color: var(--mystical-violet);
    text-shadow: 0 0 10px var(--mystical-violet);
    letter-spacing: 2px;
}

#cycleNumber {
    color: var(--ethereal-cyan);
    font-weight: 600;
    text-shadow: 0 0 15px var(--ethereal-cyan);
}

/* ═══ MYSTICAL FOOTER ═══ */
.footer {
    text-align: center;
    z-index: 0;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    max-width: 800px;
    margin-bottom: 30px;
}

/* ═══ MYSTICAL CONTROLS ═══ */
.mystical-controls {
    display: flex;
    gap: 20px;
    z-index: 10;
    align-items: center;
    justify-content: center;
    cursor: default;
    flex-shrink: 0;
    width: 100%;
}

.mystical-btn {
    position: relative;
    width: 150px;
    height: 150px;
    padding: 0;
    background: transparent;
    border: 4px solid var(--mystical-violet);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: var(--font-ethereal);
    backdrop-filter: blur(15px);
    box-shadow:
        0 0 40px rgba(139, 92, 246, 0.5),
        inset 0 0 30px rgba(139, 92, 246, 0.2);
    overflow: hidden;
}

.mystical-btn::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: conic-gradient(transparent,
            var(--mystical-violet),
            transparent);
    border-radius: 50%;
    animation: btnRotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

@keyframes btnRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.mystical-btn:hover:not(:disabled)::before {
    opacity: 0.5;
}

.mystical-btn:active {
    transform: translateY(-2px) scale(0.95);
    transition: all 0.1s ease;
}

.mystical-btn .btn-symbol:hover {
    transform: scale(1.1);
    text-shadow: 0 0 30px currentColor;
}

/* Active state symbol animation */
.btn-toggle.active .btn-symbol {
    animation: symbolPulse 2s ease-in-out infinite;
}

.btn-toggle.active .btn-runes {
    animation: runeShimmer 3s ease-in-out infinite;
}

@keyframes symbolPulse {
    0%, 100% { transform: scale(1); text-shadow: 0 0 20px currentColor; }
    50% { transform: scale(1.1); text-shadow: 0 0 35px currentColor, 0 0 50px var(--sacred-gold); }
}

@keyframes runeShimmer {
    0%, 100% { opacity: 0.8; text-shadow: 0 0 10px var(--sacred-gold); }
    50% { opacity: 1; text-shadow: 0 0 20px var(--sacred-gold), 0 0 30px var(--ethereal-cyan); }
}

.mystical-btn:hover:not(:disabled) {
    transform: translateY(-5px) scale(1.1);
    box-shadow:
        0 0 60px var(--mystical-violet),
        0 15px 40px rgba(139, 92, 246, 0.5);
    border-color: var(--ethereal-cyan);
}

.btn-toggle {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 40px rgba(16, 185, 129, 0.4),
        inset 0 0 35px rgba(16, 185, 129, 0.15);
    transition: all 0.5s ease;
}

.btn-toggle:hover:not(:disabled) {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 70px var(--aurora-green),
        0 15px 40px rgba(16, 185, 129, 0.5);
}

.btn-toggle.active {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 50px rgba(236, 72, 153, 0.5),
        inset 0 0 35px rgba(236, 72, 153, 0.2);
    animation: activeButtonPulse 2s ease-in-out infinite;
}

.btn-toggle.active::before {
    opacity: 0.4;
    animation: btnRotate 3s linear infinite;
}

.btn-toggle.active:hover {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 80px var(--celestial-pink),
        0 15px 40px rgba(236, 72, 153, 0.6);
    animation: activeButtonPulse 1.5s ease-in-out infinite;
}

@keyframes activeButtonPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 50px rgba(236, 72, 153, 0.5), inset 0 0 35px rgba(236, 72, 153, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 70px rgba(236, 72, 153, 0.7), inset 0 0 40px rgba(236, 72, 153, 0.3);
    }
}

.mystical-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.2);
}

.btn-symbol {
    font-size: 3.5rem;
    color: var(--lunar-silver);
    text-shadow: 0 0 25px currentColor;
    z-index: 2;
    position: relative;
    transition: all 0.3s ease;
}

.btn-text {
    display: none;
}

.btn-runes {
    font-size: 0.9rem;
    color: var(--sacred-gold);
    opacity: 0.8;
    letter-spacing: 2px;
    z-index: 2;
    position: relative;
    text-shadow: 0 0 12px var(--sacred-gold);
}

/* ═══ WISDOM PANEL ═══ */
.wisdom-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 5, 17, 0.9);
    border: 1px solid var(--mystical-violet);
    border-radius: 12px;
    padding: 12px;
    max-width: 280px;
    width: 280px;
    height: auto;
    font-family: var(--font-ethereal);
    backdrop-filter: blur(15px);
    box-shadow:
        0 0 20px rgba(139, 92, 246, 0.3),
        inset 0 0 20px rgba(139, 92, 246, 0.1);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.9;
    cursor: pointer;
    overflow: hidden;
}

.wisdom-panel:hover {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow:
        0 0 30px rgba(139, 92, 246, 0.4),
        inset 0 0 25px rgba(139, 92, 246, 0.15);
}

.wisdom-panel.collapsed:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 0 35px rgba(139, 92, 246, 0.6),
        inset 0 0 20px rgba(139, 92, 246, 0.3);
}

.wisdom-panel.collapsed:hover::before {
    animation-duration: 1.5s;
    text-shadow:
        0 0 20px var(--sacred-gold),
        0 0 40px var(--ethereal-cyan),
        0 0 60px var(--mystical-violet);
}

/* Collapsible wisdom panel */
.wisdom-panel.collapsed {
    width: 60px;
    height: 60px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, rgba(0, 5, 17, 0.95) 70%);
    border: none;
    outline: none;
    box-shadow:
        0 0 25px rgba(139, 92, 246, 0.5),
        inset 0 0 15px rgba(139, 92, 246, 0.2);
    position: fixed;
    bottom: 20px;
    right: 20px;
}

.wisdom-panel.collapsed::before {
    content: '✩';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: var(--sacred-gold);
    text-shadow:
        0 0 15px var(--sacred-gold),
        0 0 25px var(--ethereal-cyan);
    animation: mysticalPulse 3s ease-in-out infinite;
}

@keyframes mysticalPulse {
    0%, 100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        text-shadow: 0 0 10px var(--sacred-gold), 0 0 20px var(--ethereal-cyan);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
        text-shadow: 0 0 15px var(--sacred-gold), 0 0 30px var(--mystical-violet);
    }
}

.wisdom-panel.collapsed .wisdom-header {
    opacity: 0;
    transform: scale(0);
    margin: 0;
    padding: 0;
    border: none;
}

.wisdom-panel.collapsed .wisdom-title {
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease;
}

.wisdom-panel.collapsed .wisdom-content {
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease;
    height: 0;
    overflow: hidden;
}

.wisdom-panel.collapsed .ancient-symbol {
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

/* Expanded state transitions */
.wisdom-panel:not(.collapsed) .wisdom-title {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.1s;
}

.wisdom-panel:not(.collapsed) .wisdom-content {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.2s;
    height: auto;
}

.wisdom-panel:not(.collapsed) .ancient-symbol:last-child {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.1s;
}

.wisdom-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
}

.ancient-symbol {
    font-size: 1.2rem;
    color: var(--sacred-gold);
    text-shadow: 0 0 8px var(--sacred-gold);
    animation: ancientPulse 6s ease-in-out infinite alternate;
    transition: all 0.3s ease;
}

@keyframes ancientPulse {
    from { opacity: 0.7; }
    to { opacity: 1; text-shadow: 0 0 20px var(--sacred-gold); }
}

.wisdom-title {
    font-family: var(--font-mystical);
    font-size: 1.1rem;
    color: var(--ethereal-cyan);
    margin: 0;
    text-align: center;
    letter-spacing: 1px;
}

.wisdom-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    transition: all 0.3s ease;
}

.breath-phase {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 8px;
    background: rgba(139, 92, 246, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.phase-rune {
    font-size: 1.3rem;
    color: var(--mystical-violet);
    text-shadow: 0 0 8px var(--mystical-violet);
    flex-shrink: 0;
    margin-top: 2px;
}

.phase-description {
    flex: 1;
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--lunar-silver);
}

.phase-description strong {
    color: var(--ethereal-cyan);
    font-weight: 600;
}

.phase-description em {
    color: var(--sacred-gold);
    font-style: italic;
    opacity: 0.9;
    font-size: 0.75rem;
}

/* Control buttons - Simplified */
.controls {
    display: flex;
    gap: 20px;
    z-index: 10;
}

.btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    min-width: 150px;
    color: white;
}

.btn-start {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-stop {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
}

.btn-start:hover:not(:disabled) {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-stop:hover:not(:disabled) {
    background: linear-gradient(45deg, #d32f2f, #c62828);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Instructions panel - Removed duplicate styles */

/* Phase-specific styling */
.phase-inhale .phase-indicator {
    color: #4CAF50;
    text-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.phase-hold .phase-indicator {
    color: #FFD700;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.phase-exhale .phase-indicator {
    color: #9C27B0;
    text-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
}

/* Responsive design */
@media (max-width: 768px) {
    .app-container {
        padding: 8px 8px 20px 8px;
        min-height: 100vh;
        height: auto;
    }

    /* Hide wisdom panel on mobile */
    .wisdom-panel {
        display: none;
    }

    .title {
        font-size: 1.8rem;
    }

    .mystical-text {
        font-size: 0.9rem;
    }

    .rune-divider {
        font-size: 0.8rem;
        letter-spacing: 2px;
    }

    .mystical-btn {
        width: 120px;
        height: 120px;
        border-width: 3px;
        gap: 8px;
    }

    .btn-symbol {
        font-size: 2.8rem;
    }

    .btn-runes {
        font-size: 0.7rem;
    }

    .footer {
        margin-bottom: 0;
        padding-bottom: 10px;
    }

    .moon-portal {
        width: 200px;
        height: 200px;
    }

    .moon-core {
        width: 160px;
        height: 160px;
    }

    .moon-inner-light {
        width: 110px;
        height: 110px;
    }

    .moon-runes {
        font-size: 1.1rem;
        letter-spacing: 4px;
    }

    .phase-symbol {
        font-size: 2rem;
    }

    .phase-text {
        font-size: 1.1rem;
    }

    .cosmic-timer {
        font-size: 2.5rem;
        min-height: 50px;
    }
}

@media (max-height: 700px) {
    .app-container {
        padding: 5px;
        gap: 5px;
    }

    .sacred-symbol.top-symbol {
        font-size: 2rem;
        margin-bottom: 5px;
    }

    .title {
        font-size: 1.6rem;
        margin-bottom: 5px;
    }

    .mystical-text {
        font-size: 0.8rem;
        margin-bottom: 3px;
    }

    .rune-divider {
        font-size: 0.7rem;
    }

    .moon-portal {
        width: 180px;
        height: 180px;
    }

    .moon-core {
        width: 145px;
        height: 145px;
    }

    .moon-inner-light {
        width: 100px;
        height: 100px;
    }

    .moon-runes {
        font-size: 1rem;
    }

    .moon-container {
        margin-bottom: 10px;
    }

    .instruction-container {
        margin-top: 10px;
    }

    .mystical-phase {
        margin-bottom: 8px;
    }

    .phase-symbol {
        font-size: 1.8rem;
        margin-bottom: 5px;
    }

    .phase-text {
        font-size: 1rem;
        min-height: 30px;
    }

    .cosmic-timer {
        font-size: 2rem;
        min-height: 40px;
        margin-bottom: 8px;
    }

    .mystical-controls {
        margin-top: 10px;
    }

    .mystical-btn {
        padding: 10px 15px;
    }

    .wisdom-panel {
        padding: 10px;
        margin-top: 10px;
    }

    .wisdom-header {
        margin-bottom: 8px;
        padding-bottom: 8px;
    }

    .breath-phase {
        padding: 6px;
    }

    .phase-description {
        font-size: 0.7rem;
        line-height: 1.3;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .app-container {
        padding: 5px 5px 15px 5px;
        min-height: 100vh;
    }

    .title {
        font-size: 1.5rem;
        letter-spacing: 1px;
    }

    .sacred-symbol.top-symbol {
        font-size: 1.8rem;
    }

    .mystical-text {
        font-size: 0.8rem;
    }

    .moon-portal {
        width: 150px;
        height: 150px;
    }

    .moon-core {
        width: 120px;
        height: 120px;
    }

    .cosmic-timer {
        font-size: 2rem;
    }

    .phase-text {
        font-size: 1rem;
    }

    .mystical-btn {
        width: 100px;
        height: 100px;
        gap: 6px;
    }

    .btn-symbol {
        font-size: 2.3rem;
    }

    .btn-runes {
        font-size: 0.6rem;
    }

    .footer {
        margin-bottom: 5px;
    }

}

/* Very short screens */
@media (max-height: 500px) {
    .app-container {
        gap: 3px;
        padding: 3px 3px 10px 3px;
        min-height: 100vh;
    }

    .header {
        margin-bottom: 5px;
    }

    .footer {
        margin-bottom: 0;
    }

    .mystical-btn {
        width: 90px;
        height: 90px;
        gap: 5px;
    }

    .btn-symbol {
        font-size: 2rem;
    }

    .btn-runes {
        font-size: 0.5rem;
    }

    .sacred-symbol.top-symbol {
        font-size: 1.5rem;
        margin-bottom: 3px;
    }

    .title {
        font-size: 1.3rem;
        margin-bottom: 3px;
    }

    .subtitle-container {
        margin-top: 3px;
    }

    .mystical-text {
        font-size: 0.7rem;
        margin-bottom: 2px;
    }

    .moon-portal {
        width: 80px;
        height: 80px;
    }

    .moon-core {
        width: 65px;
        height: 65px;
    }

    .moon-container {
        margin-bottom: 5px;
    }

    .instruction-container {
        margin-top: 5px;
    }

    .phase-symbol {
        font-size: 1.5rem;
        margin-bottom: 3px;
    }

    .phase-text {
        font-size: 0.9rem;
        min-height: 25px;
    }

    .cosmic-timer {
        font-size: 1.8rem;
        min-height: 35px;
        margin-bottom: 5px;
    }

    .mystical-controls {
        margin-top: 5px;
    }
}

/* Phase-specific button states */
.cosmic-phase-inhale .btn-toggle.active {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 50px rgba(16, 185, 129, 0.6),
        inset 0 0 35px rgba(16, 185, 129, 0.2);
}

.cosmic-phase-inhale .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--aurora-green),
            transparent);
}

.cosmic-phase-hold .btn-toggle.active {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 60px rgba(251, 191, 36, 0.7),
        inset 0 0 40px rgba(251, 191, 36, 0.25);
}

.cosmic-phase-hold .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--sacred-gold),
            transparent);
}

.cosmic-phase-exhale .btn-toggle.active {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 55px rgba(236, 72, 153, 0.6),
        inset 0 0 35px rgba(236, 72, 153, 0.2);
}

.cosmic-phase-exhale .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--celestial-pink),
            transparent);
}

/* Enhanced active state transitions */
.btn-toggle.active {
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-toggle.active .btn-symbol {
    transition: all 0.5s ease;
}

.btn-toggle.active .btn-runes {
    transition: all 0.5s ease;
}

/* Exhale phase specific energy pulse */
.cosmic-phase-exhale .energy-pulse {
    animation: energyPulseExhale 4s linear infinite;
}

.cosmic-phase-exhale .pulse-1 {
    animation-delay: 0s;
}

.cosmic-phase-exhale .pulse-2 {
    animation-delay: 1.3s;
}

.cosmic-phase-exhale .pulse-3 {
    animation-delay: 2.6s;
}

@keyframes energyPulseExhale {
    0% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(0.9);
    }

    30% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1.1);
    }

    70% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1.4);
    }

    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.8);
    }
}

/* Mobile browser optimizations */
@media (max-width: 768px) {
    .app-container {
        padding-bottom: max(20px, env(safe-area-inset-bottom, 20px));
    }
}

@media (max-width: 768px) and (max-height: 600px) {
    .footer {
        position: relative;
        margin-bottom: 0;
        padding-bottom: 15px;
    }
    .mystical-btn {
        margin-bottom: 5px;
    }
}

/* Fix for mobile browsers with bottom bars */
@supports (height: 100dvh) {
    @media (max-width: 768px) {
        .app-container {
            height: 100dvh;
            min-height: 100dvh;
        }
    }
}