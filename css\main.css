/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - MAIN CSS
   Modular CSS architecture for lunar breathing meditation
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* Import all CSS modules in order of dependency */
@import url('./base.css');          /* Base styles, variables, and reset */
@import url('./animations.css');    /* All keyframe animations */
@import url('./background.css');    /* Cosmic background effects */
@import url('./layout.css');        /* Layout and typography */
@import url('./moon.css');          /* Moon portal and chakra styles */
@import url('./controls.css');      /* Controls and wisdom panel */
@import url('./responsive.css');    /* Responsive design */

/* 
   CSS Module Structure:
   
   1. base.css - Foundation styles
      - CSS reset and normalization
      - CSS custom properties (variables)
      - Basic body and html styles
   
   2. animations.css - All animations
      - Cosmic background animations
      - Moon portal animations
      - Button and UI animations
      - Breathing wave animations
   
   3. background.css - Background effects
      - Stars, nebula, aurora
      - Cosmic waves and energy fields
      - Background gradients
   
   4. layout.css - Layout and typography
      - App container and structure
      - Header, footer, and main content
      - Typography and text effects
   
   5. moon.css - Moon portal system
      - Moon portal and phases
      - Chakra points
      - Energy pulses
   
   6. controls.css - Interactive elements
      - Mystical buttons
      - Wisdom panel
      - Control states
   
   7. responsive.css - Mobile optimization
      - Tablet and mobile styles
      - Responsive breakpoints
      - Touch-friendly adjustments
*/
