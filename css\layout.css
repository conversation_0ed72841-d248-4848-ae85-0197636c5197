/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - LAYOUT STYLES
   App container, header, footer, and main layout components
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* App container - responsive fullscreen */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 15px 10px;
    overflow: hidden;
}

/* ═══ MYSTICAL HEADER ═══ */
.header {
    text-align: center;
    z-index: 10;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    max-width: 800px;
    margin-bottom: 80px;
}

.sacred-symbol.top-symbol {
    font-size: 2.5rem;
    color: var(--sacred-gold);
    text-shadow: 0 0 20px var(--sacred-gold);
    margin-bottom: 10px;
    display: block;
    animation: sacredPulse 4s ease-in-out infinite;
}

.title {
    font-family: var(--font-mystical);
    font-size: 2.2rem;
    font-weight: 400;
    margin-bottom: 10px;
    color: var(--lunar-silver);
    text-shadow:
        0 0 20px var(--mystical-violet),
        0 0 40px var(--ethereal-cyan);
    letter-spacing: 2px;
    animation: titleGlow 6s ease-in-out infinite alternate;
    line-height: 1.2;
}

.subtitle-container {
    margin-top: 8px;
}

.mystical-text {
    font-family: var(--font-ethereal);
    font-size: 1rem;
    color: var(--ethereal-cyan);
    font-style: italic;
    text-shadow: 0 0 15px var(--ethereal-cyan);
    display: block;
    margin-bottom: 5px;
}

.rune-divider {
    font-size: 0.9rem;
    color: var(--sacred-gold);
    opacity: 0.7;
    letter-spacing: 4px;
    animation: runePulse 8s ease-in-out infinite;
}

/* ═══ MAIN MYSTICAL CONTAINER ═══ */
.breathing-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 600px;
}

/* ═══ MYSTICAL INSTRUCTIONS ═══ */
.instruction-container {
    text-align: center;
    z-index: 10;
    position: relative;
    flex-shrink: 0;
    width: 100%;
}

.mystical-phase {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
    animation: phaseShimmer 8s ease-in-out infinite alternate;
}

.phase-symbol {
    font-size: 2.5rem;
    margin-bottom: 8px;
    text-shadow: 0 0 20px currentColor;
    animation: symbolRotate 20s linear infinite;
}

.phase-text {
    font-family: var(--font-mystical);
    font-size: 1.3rem;
    font-weight: 400;
    color: var(--lunar-silver);
    text-shadow: 0 0 15px currentColor;
    letter-spacing: 1px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 1s ease;
}

.cosmic-timer {
    font-family: var(--font-mystical);
    font-size: 3rem;
    font-weight: 300;
    color: var(--ethereal-cyan);
    text-shadow:
        0 0 20px var(--ethereal-cyan),
        0 0 40px var(--mystical-violet);
    margin-bottom: 10px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: timerPulse 4s ease-in-out infinite;
}

.cycle-tracker {
    font-family: var(--font-ethereal);
    font-size: 1.3rem;
    color: var(--sacred-gold);
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.rune-counter {
    color: var(--mystical-violet);
    text-shadow: 0 0 10px var(--mystical-violet);
    letter-spacing: 2px;
}

#cycleNumber {
    color: var(--ethereal-cyan);
    font-weight: 600;
    text-shadow: 0 0 15px var(--ethereal-cyan);
}

/* ═══ MYSTICAL FOOTER ═══ */
.footer {
    text-align: center;
    z-index: 0;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    max-width: 800px;
    margin-bottom: 30px;
}

/* Phase-specific styling */
.phase-inhale .phase-indicator {
    color: #4CAF50;
    text-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.phase-hold .phase-indicator {
    color: #FFD700;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.phase-exhale .phase-indicator {
    color: #9C27B0;
    text-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
}
