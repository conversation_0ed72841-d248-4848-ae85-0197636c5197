/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - RESPONSIVE DESIGN
   Mobile and tablet optimizations
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* ═══ TABLET AND MOBILE ═══ */
@media (max-width: 768px) {
    .app-container {
        padding: 8px 8px 20px 8px;
        min-height: 100vh;
        height: auto;
    }

    /* Hide wisdom panel on mobile */
    .wisdom-panel {
        display: none;
    }

    .title {
        font-size: 1.8rem;
    }

    .mystical-text {
        font-size: 0.9rem;
    }

    .rune-divider {
        font-size: 0.8rem;
        letter-spacing: 2px;
    }

    .mystical-btn {
        width: 120px;
        height: 120px;
        border-width: 3px;
        gap: 8px;
    }

    .btn-symbol {
        font-size: 2.8rem;
    }

    .btn-runes {
        font-size: 0.7rem;
    }

    .footer {
        margin-bottom: 0;
        padding-bottom: 10px;
    }

    .moon-portal {
        width: 200px;
        height: 200px;
    }

    .moon-core {
        width: 160px;
        height: 160px;
    }

    .moon-inner-light {
        width: 110px;
        height: 110px;
    }

    .moon-runes {
        font-size: 1.1rem;
        letter-spacing: 4px;
    }

    .phase-symbol {
        font-size: 2rem;
    }

    .phase-text {
        font-size: 1.1rem;
    }

    .cosmic-timer {
        font-size: 2.5rem;
        min-height: 50px;
    }
}

/* ═══ SHORT SCREENS ═══ */
@media (max-height: 700px) {
    .app-container {
        padding: 5px;
        gap: 5px;
    }

    .sacred-symbol.top-symbol {
        font-size: 2rem;
        margin-bottom: 5px;
    }

    .title {
        font-size: 1.6rem;
        margin-bottom: 5px;
    }

    .mystical-text {
        font-size: 0.8rem;
        margin-bottom: 3px;
    }

    .rune-divider {
        font-size: 0.7rem;
    }

    .moon-portal {
        width: 180px;
        height: 180px;
    }

    .moon-core {
        width: 145px;
        height: 145px;
    }

    .moon-inner-light {
        width: 100px;
        height: 100px;
    }

    .moon-runes {
        font-size: 1rem;
    }

    .moon-container {
        margin-bottom: 10px;
    }

    .instruction-container {
        margin-top: 10px;
    }

    .mystical-phase {
        margin-bottom: 8px;
    }

    .phase-symbol {
        font-size: 1.8rem;
        margin-bottom: 5px;
    }

    .phase-text {
        font-size: 1rem;
        min-height: 30px;
    }

    .cosmic-timer {
        font-size: 2rem;
        min-height: 40px;
        margin-bottom: 8px;
    }

    .mystical-controls {
        margin-top: 10px;
    }

    .mystical-btn {
        padding: 10px 15px;
    }

    .wisdom-panel {
        padding: 10px;
        margin-top: 10px;
    }

    .wisdom-header {
        margin-bottom: 8px;
        padding-bottom: 8px;
    }

    .breath-phase {
        padding: 6px;
    }

    .phase-description {
        font-size: 0.7rem;
        line-height: 1.3;
    }
}

/* ═══ SMALL MOBILE ═══ */
@media (max-width: 480px) {
    .app-container {
        padding: 5px 5px 15px 5px;
        min-height: 100vh;
    }

    .title {
        font-size: 1.5rem;
        letter-spacing: 1px;
    }

    .sacred-symbol.top-symbol {
        font-size: 1.8rem;
    }

    .mystical-text {
        font-size: 0.8rem;
    }

    .moon-portal {
        width: 150px;
        height: 150px;
    }

    .moon-core {
        width: 120px;
        height: 120px;
    }

    .cosmic-timer {
        font-size: 2rem;
    }

    .phase-text {
        font-size: 1rem;
    }

    .mystical-btn {
        width: 100px;
        height: 100px;
        gap: 6px;
    }

    .btn-symbol {
        font-size: 2.3rem;
    }

    .btn-runes {
        font-size: 0.6rem;
    }

    .footer {
        margin-bottom: 5px;
    }
}

/* ═══ VERY SHORT SCREENS ═══ */
@media (max-height: 500px) {
    .app-container {
        gap: 3px;
        padding: 3px 3px 10px 3px;
        min-height: 100vh;
    }

    .header {
        margin-bottom: 5px;
    }

    .footer {
        margin-bottom: 0;
    }

    .mystical-btn {
        width: 90px;
        height: 90px;
        gap: 5px;
    }

    .btn-symbol {
        font-size: 2rem;
    }

    .btn-runes {
        font-size: 0.5rem;
    }

    .sacred-symbol.top-symbol {
        font-size: 1.5rem;
        margin-bottom: 3px;
    }

    .title {
        font-size: 1.3rem;
        margin-bottom: 3px;
    }

    .subtitle-container {
        margin-top: 3px;
    }

    .mystical-text {
        font-size: 0.7rem;
        margin-bottom: 2px;
    }

    .moon-portal {
        width: 80px;
        height: 80px;
    }

    .moon-core {
        width: 65px;
        height: 65px;
    }

    .moon-container {
        margin-bottom: 5px;
    }

    .instruction-container {
        margin-top: 5px;
    }

    .phase-symbol {
        font-size: 1.5rem;
        margin-bottom: 3px;
    }

    .phase-text {
        font-size: 0.9rem;
        min-height: 25px;
    }

    .cosmic-timer {
        font-size: 1.8rem;
        min-height: 35px;
        margin-bottom: 5px;
    }

    .mystical-controls {
        margin-top: 5px;
    }
}

/* ═══ MOBILE BROWSER OPTIMIZATIONS ═══ */
@media (max-width: 768px) {
    .app-container {
        padding-bottom: max(20px, env(safe-area-inset-bottom, 20px));
    }
}

@media (max-width: 768px) and (max-height: 600px) {
    .footer {
        position: relative;
        margin-bottom: 0;
        padding-bottom: 15px;
    }
    .mystical-btn {
        margin-bottom: 5px;
    }
}

/* Fix for mobile browsers with bottom bars */
@supports (height: 100dvh) {
    @media (max-width: 768px) {
        .app-container {
            height: 100dvh;
            min-height: 100dvh;
        }
    }
}
