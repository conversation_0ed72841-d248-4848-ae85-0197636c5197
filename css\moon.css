/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - MOON PORTAL STYLES
   Moon portal, energy fields, chakra points, and moon phases
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* ═══ LUNAR PORTAL ═══ */
.moon-container {
    position: relative;
    margin-bottom: 10px;
    z-index: 5;
    flex-shrink: 0;
}

.energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 380px;
    height: 380px;
    z-index: 1;
}

.energy-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.pulse-1 {
    width: 350px;
    height: 350px;
    border-color: var(--mystical-violet);
    animation: energyPulse 4s linear infinite;
}

.pulse-2 {
    width: 300px;
    height: 300px;
    border-color: var(--ethereal-cyan);
    animation: energyPulse 4s linear infinite 1.3s;
}

.pulse-3 {
    width: 250px;
    height: 250px;
    border-color: var(--sacred-gold);
    animation: energyPulse 4s linear infinite 2.6s;
}

.moon-portal {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    position: relative;
    z-index: 3;
    background: var(--lunar-silver);
    box-shadow:
        0 0 80px var(--mystical-violet),
        0 0 120px rgba(139, 92, 246, 0.3),
        inset 0 0 50px var(--deep-space);
    transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.moon-portal:hover {
    transform: scale(1.02);
    box-shadow:
        0 0 90px var(--mystical-violet),
        0 0 140px rgba(139, 92, 246, 0.4),
        inset 0 0 55px var(--deep-space);
}

.moon-portal:active {
    transform: scale(0.98);
    transition: all 0.1s ease;
}

.moon-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, var(--lunar-silver), var(--deep-space));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    overflow: hidden;
}

.moon-inner-light {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--sacred-gold) 0%, transparent 70%);
    opacity: 0.6;
    animation: innerLightPulse 8s ease-in-out infinite;
}

.moon-runes {
    font-size: 2.2rem;
    color: var(--mystical-violet);
    text-shadow: 0 0 25px var(--mystical-violet);
    z-index: 2;
    letter-spacing: 10px;
    animation: runesGlow 6s ease-in-out infinite alternate;
}

.moon-aura {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 380px;
    height: 380px;
    border-radius: 50%;
    background: radial-gradient(circle, transparent 40%, var(--mystical-violet) 70%, transparent 100%);
    opacity: 0.3;
    animation: auraShimmer 10s ease-in-out infinite;
    z-index: 0;
}

/* ═══ CHAKRA POINTS ═══ */
.chakra-points {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 420px;
    height: 420px;
    z-index: 4;
}

.chakra {
    position: absolute;
    width: 25px;
    height: 25px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-shadow: 0 0 15px currentColor;
    animation: chakraPulse 8s ease-in-out infinite;
}

.chakra-1 {
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: var(--celestial-pink);
    animation-delay: 0s;
}

.chakra-2 {
    top: 50%;
    right: 10%;
    transform: translateY(-50%);
    color: var(--sacred-gold);
    animation-delay: 2s;
}

.chakra-3 {
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: var(--aurora-green);
    animation-delay: 4s;
}

.chakra-4 {
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    color: var(--ethereal-cyan);
    animation-delay: 6s;
}

/* ═══ MOON PHASE TRANSFORMATIONS ═══ */
.moon-portal.new-moon {
    background: radial-gradient(circle at 30% 30%, var(--deep-space), var(--cosmic-void));
    box-shadow:
        0 0 80px var(--mystical-violet),
        0 0 120px rgba(139, 92, 246, 0.4),
        inset 0 0 80px var(--mystical-violet);
    transform: scale(0.9);
}

.moon-portal.new-moon .moon-core {
    background: radial-gradient(circle at 30% 30%, var(--deep-space), var(--cosmic-void));
}

.moon-portal.new-moon .moon-inner-light {
    background: radial-gradient(circle, var(--mystical-violet) 0%, transparent 60%);
    opacity: 0.8;
}

.moon-portal.full-moon {
    background: radial-gradient(circle at 30% 30%, var(--lunar-silver), #f8fafc);
    box-shadow:
        0 0 120px var(--sacred-gold),
        0 0 180px var(--ethereal-cyan),
        0 0 240px rgba(251, 191, 36, 0.3),
        inset 0 0 60px var(--mystical-violet);
    transform: scale(1.1);
}

.moon-portal.full-moon .moon-core {
    background: radial-gradient(circle at 30% 30%, #ffffff, var(--lunar-silver));
}

.moon-portal.full-moon .moon-inner-light {
    background: radial-gradient(circle, var(--sacred-gold) 0%, transparent 80%);
    opacity: 1;
    animation-duration: 4s;
}

.moon-portal.waning-moon {
    background: linear-gradient(90deg, var(--cosmic-void) 45%, var(--lunar-silver) 55%);
    box-shadow:
        0 0 100px var(--celestial-pink),
        0 0 140px rgba(236, 72, 153, 0.4),
        inset 0 0 70px var(--aurora-green);
    transform: scale(0.95);
}

.moon-portal.waning-moon .moon-core {
    background: linear-gradient(90deg, var(--deep-space) 45%, var(--lunar-silver) 55%);
}

.moon-portal.waning-moon .moon-inner-light {
    background: radial-gradient(circle, var(--celestial-pink) 0%, transparent 70%);
    opacity: 0.7;
}

/* ═══ PHASE-SPECIFIC ENERGY PULSES ═══ */

/* Exhale phase - Steady expanding pulses matching 8s breath rhythm */
.cosmic-phase-exhale .energy-pulse {
    animation: energyPulseExhale 8s linear infinite;
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 20px var(--celestial-pink),
        inset 0 0 10px rgba(236, 72, 153, 0.2);
}

.cosmic-phase-exhale .pulse-1 {
    animation-delay: 0s;
    border-color: var(--celestial-pink);
}

.cosmic-phase-exhale .pulse-2 {
    animation-delay: 2.67s;  /* 8s / 3 = 2.67s intervals */
    border-color: var(--mystical-violet);
}

.cosmic-phase-exhale .pulse-3 {
    animation-delay: 5.33s;  /* 2 * 2.67s = 5.33s */
    border-color: var(--ethereal-cyan);
}

/* Inhale phase - Contracting pulses */
.cosmic-phase-inhale .energy-pulse {
    animation: energyPulse 4s ease-in-out infinite;
    border-color: var(--aurora-green);
}

/* Hold phase - Steady pulsing */
.cosmic-phase-hold .energy-pulse {
    animation: energyPulse 6s ease-in-out infinite;
    border-color: var(--sacred-gold);
}

/* Enhanced exhale moon portal effects */
.cosmic-phase-exhale .moon-portal {
    box-shadow:
        0 0 100px var(--celestial-pink),
        0 0 150px rgba(236, 72, 153, 0.4),
        inset 0 0 60px var(--mystical-violet);
    transform: scale(1.05);
    transition: all 2s ease-out;
}

.cosmic-phase-exhale .moon-inner-light {
    animation: innerLightPulse 4s ease-out infinite;
    background: radial-gradient(circle, var(--celestial-pink) 0%, transparent 80%);
    opacity: 0.9;
}

.cosmic-phase-exhale .moon-runes {
    color: var(--celestial-pink);
    text-shadow:
        0 0 30px var(--celestial-pink),
        0 0 50px var(--mystical-violet);
    animation: runesGlow 4s ease-out infinite alternate;
}
