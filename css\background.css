/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - BACKGROUND EFFECTS
   Cosmic background, stars, nebula, and aurora effects
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* ═══ COSMIC BACKGROUND ═══ */
.cosmic-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1;
    pointer-events: none;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(2px 2px at 20px 30px, #06b6d4, transparent),
        radial-gradient(2px 2px at 40px 70px, #e2e8f0, transparent),
        radial-gradient(1px 1px at 90px 40px, #fbbf24, transparent),
        radial-gradient(1px 1px at 130px 80px, #8b5cf6, transparent),
        radial-gradient(2px 2px at 160px 30px, #ec4899, transparent),
        radial-gradient(1px 1px at 180px 60px, #10b981, transparent),
        radial-gradient(1.5px 1.5px at 50px 90px, #06b6d4, transparent),
        radial-gradient(1px 1px at 170px 10px, #e2e8f0, transparent);
    background-repeat: repeat;
    background-size: 200px 120px;
    animation: starsTwinkle 3s ease-in-out infinite alternate, starsFloat 20s linear infinite;
}

.nebula {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 80% 50% at 30% 70%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
        radial-gradient(ellipse 60% 40% at 70% 30%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse 90% 30% at 50% 90%, rgba(236, 72, 153, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse 70% 60% at 20% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 60%),
        radial-gradient(ellipse 50% 80% at 80% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 70%);
    animation: nebulaFlow 45s ease-in-out infinite, nebulaDrift 90s linear infinite;
}

.aurora {
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg,
            transparent 30%,
            rgba(16, 185, 129, 0.05) 50%,
            transparent 70%),
        linear-gradient(-45deg,
            transparent 40%,
            rgba(251, 191, 36, 0.04) 60%,
            transparent 80%);
    animation: auroraShimmer 30s ease-in-out infinite;
}

/* ═══ COSMIC ENERGY WAVES ═══ */
.cosmic-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 1;
}

.cosmic-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 4px solid;
    opacity: 0;
    transform: translate(-50%, -50%);
}

.wave-ethereal {
    border-color: var(--ethereal-cyan);
    box-shadow:
        0 0 30px var(--ethereal-cyan),
        inset 0 0 20px rgba(6, 182, 212, 0.1);
    animation-delay: 0s;
}

.wave-astral {
    border-color: var(--mystical-violet);
    box-shadow:
        0 0 25px var(--mystical-violet),
        inset 0 0 15px rgba(139, 92, 246, 0.1);
    animation-delay: 1.2s;
}

.wave-spiritual {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 35px var(--sacred-gold),
        inset 0 0 25px rgba(251, 191, 36, 0.1);
    animation-delay: 2.4s;
}

.wave-divine {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 28px var(--celestial-pink),
        inset 0 0 18px rgba(236, 72, 153, 0.1);
    animation-delay: 3.6s;
}

.wave-cosmic {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 32px var(--aurora-green),
        inset 0 0 22px rgba(16, 185, 129, 0.1);
    animation-delay: 4.8s;
}

/* Phase-specific wave animations */
.cosmic-wave.active.phase-inhale {
    animation: cosmicWaveInhale 4s linear infinite;
}

.cosmic-wave.active.phase-hold {
    animation: cosmicWaveHold 7s ease-in-out infinite;
}

.cosmic-wave.active.phase-exhale {
    animation: cosmicWaveExhale 8s linear infinite;
    opacity: 0.8;
}

/* Enhanced exhale wave effects - evenly distributed across 8s */
.cosmic-phase-exhale .wave-ethereal {
    animation-delay: 0s;
    border-color: var(--celestial-pink);
}

.cosmic-phase-exhale .wave-astral {
    animation-delay: 1.6s;  /* 8s / 5 waves = 1.6s intervals */
    border-color: var(--mystical-violet);
}

.cosmic-phase-exhale .wave-spiritual {
    animation-delay: 3.2s;
    border-color: var(--ethereal-cyan);
}

.cosmic-phase-exhale .wave-divine {
    animation-delay: 4.8s;
    border-color: var(--aurora-green);
}

.cosmic-phase-exhale .wave-cosmic {
    animation-delay: 6.4s;
    border-color: var(--sacred-gold);
}

/* Default state */
.cosmic-wave.active {
    animation: cosmicWaveHold 8s ease-in-out infinite;
}
