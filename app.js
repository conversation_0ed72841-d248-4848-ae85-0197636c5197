/**
 * ═══════════════════════════════════════════════════════════════════════════════════
 * SACRED BREATH - MYSTICAL JAVASCRIPT ALCHEMY
 * A cosmic journey through lunar breathing meditation
 * ═══════════════════════════════════════════════════════════════════════════════════
 */

class MysticalBreathingApp {
    constructor() {
        this.isRunning = false;
        this.currentPhase = 'ready'; // ready, inhale, hold, exhale
        this.currentCycle = 0;
        this.phaseTimer = 0;
        this.intervalId = null;
        this.waveIntervalId = null;
        
        // Sacred breathing durations
        this.phaseDurations = {
            inhale: 4,   // Draw cosmic essence
            hold: 7,     // Contain divine energy  
            exhale: 8    // Release to the void
        };
        
        // Mystical phase configurations
        this.phaseConfig = {
            ready: {
                symbol: '◉',
                text: 'READY',
                moonClass: '',
                color: 'var(--lunar-silver)'
            },
            inhale: {
                symbol: '☽',
                text: 'INHALE',
                moonClass: 'new-moon',
                color: 'var(--aurora-green)'
            },
            hold: {
                symbol: '○',
                text: 'HOLD',
                moonClass: 'full-moon',
                color: 'var(--sacred-gold)'
            },
            exhale: {
                symbol: '☾',
                text: 'EXHALE',
                moonClass: 'waning-moon',
                color: 'var(--celestial-pink)'
            }
        };
        
        // DOM elements
        this.elements = {
            toggleBtn: document.getElementById('toggleBtn'),
            btnSymbol: document.getElementById('btnSymbol'),
            btnText: document.getElementById('btnText'),
            btnRunes: document.getElementById('btnRunes'),
            moonPortal: document.getElementById('moon'),
            phaseSymbol: document.getElementById('phaseSymbol'),
            phaseText: document.querySelector('.phase-text'),
            timerDisplay: document.getElementById('timerDisplay'),
            cycleNumber: document.getElementById('cycleNumber'),
            cosmicWaves: document.getElementById('cosmicWaves'),
            appContainer: document.querySelector('.app-container'),
            energyField: document.getElementById('energyField'),
            wisdomPanel: document.getElementById('wisdomPanel')
        };
        
        this.initializeEventListeners();
        this.initializeCosmicEffects();
        this.initializeWisdomPanel();
        this.resetToReady();
    }
    
    initializeEventListeners() {
        // Mystical toggle button control
        this.elements.toggleBtn.addEventListener('click', () => {
            if (this.isRunning) {
                this.returnToVoid();
            } else {
                this.beginJourney();
            }
        });
        
        // Sacred keyboard incantations
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                if (this.isRunning) {
                    this.returnToVoid();
                } else {
                    this.beginJourney();
                }
            } else if (e.code === 'Escape') {
                this.returnToVoid();
            }
        });
        
        // Touch gestures for mobile mystics
        this.initializeTouchGestures();
    }

    initializeWisdomPanel() {
        if (this.elements.wisdomPanel) {
            // Set default collapsed state
            this.elements.wisdomPanel.classList.add('collapsed');

            this.elements.wisdomPanel.addEventListener('click', () => {
                this.toggleWisdomPanel();
            });
        }
    }

    toggleWisdomPanel() {
        if (!this.elements.wisdomPanel) return;

        const isCollapsed = this.elements.wisdomPanel.classList.contains('collapsed');

        if (isCollapsed) {
            this.elements.wisdomPanel.classList.remove('collapsed');
        } else {
            this.elements.wisdomPanel.classList.add('collapsed');
        }
    }
    
    initializeTouchGestures() {
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const touchEndY = e.changedTouches[0].clientY;
            const deltaY = touchStartY - touchEndY;
            
            // Swipe up to ascend, swipe down to descend
            if (Math.abs(deltaY) > 60) {
                if (deltaY > 0 && !this.isRunning) {
                    this.beginJourney();
                } else if (deltaY < 0 && this.isRunning) {
                    this.returnToVoid();
                }
            }
        });
    }
    
    initializeCosmicEffects() {
        // Initialize cosmic wave animations
        if (this.elements.cosmicWaves) {
            const waves = this.elements.cosmicWaves.querySelectorAll('.cosmic-wave');
            waves.forEach((wave, index) => {
                wave.style.animationDelay = `${index * 0.6}s`;
            });
        }
        
        // Initialize energy field pulses
        const energyPulses = document.querySelectorAll('.energy-pulse');
        energyPulses.forEach((pulse, index) => {
            pulse.style.animationDelay = `${index * 1.2}s`;
        });
    }
    
    beginJourney() {
        if (this.isRunning) return;
        
        console.log('🌙 Sacred breathing journey begins...');
        
        this.isRunning = true;
        this.currentCycle = 0;
        this.activateCosmicEnergies();
        this.startNewCycle();
        
        // Update mystical button state
        this.updateToggleButton();
    }
    
    returnToVoid() {
        if (!this.isRunning) return;
        
        console.log('🌑 Returning to the cosmic void...');
        
        this.isRunning = false;
        this.currentPhase = 'ready';
        
        // Clear all cosmic timers
        this.clearAllTimers();
        
        // Reset mystical interface
        this.resetToReady();
        this.deactivateCosmicEnergies();
        
        // Update mystical button state
        this.updateToggleButton();
    }
    
    clearAllTimers() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        if (this.waveIntervalId) {
            clearInterval(this.waveIntervalId);
            this.waveIntervalId = null;
        }
    }
    
    resetToReady() {
        this.updatePhaseDisplay('ready');
        this.elements.timerDisplay.textContent = '∞';
        this.elements.cycleNumber.textContent = '0';
        this.updateMoonPortal('ready');
        this.elements.appContainer.className = 'app-container';
    }
    
    startNewCycle() {
        if (!this.isRunning) return;
        
        this.currentCycle++;
        this.elements.cycleNumber.textContent = this.currentCycle.toString();
        
        console.log(`🔄 Beginning sacred cycle ${this.currentCycle}`);
        
        // Begin with the inhale phase (New Moon)
        this.enterPhase('inhale');
    }
    
    enterPhase(phase) {
        if (!this.isRunning) return;

        this.currentPhase = phase;
        this.phaseTimer = this.phaseDurations[phase];

        console.log(`${this.getPhaseEmoji(phase)} Entering ${phase} phase`);

        // Use requestAnimationFrame for smooth transitions
        requestAnimationFrame(() => {
            // Update all mystical displays
            this.updatePhaseDisplay(phase);
            this.updateMoonPortal(phase);
            this.updateCosmicContainer(phase);
        });

        // Begin the sacred countdown
        this.startPhaseTimer();
    }

    getPhaseEmoji(phase) {
        const emojis = {
            inhale: '🌑',
            hold: '🌕', 
            exhale: '🌘'
        };
        return emojis[phase] || '🌙';
    }
    
    startPhaseTimer() {
        // Update timer display immediately
        this.elements.timerDisplay.textContent = this.phaseTimer.toString();

        // Start the mystical countdown
        this.intervalId = setInterval(() => {
            if (!this.isRunning) return;

            this.phaseTimer--;
            this.elements.timerDisplay.textContent = this.phaseTimer.toString();

            // Phase transition at zero - treat all phases the same way
            if (this.phaseTimer <= 0) {
                clearInterval(this.intervalId);
                this.transitionToNextPhase();
            }
        }, 1000);
    }

    transitionToNextPhase() {
        if (!this.isRunning) return;

        if (this.currentPhase === 'exhale') {
            // Exhale complete, allow animation to finish naturally
            this.elements.timerDisplay.textContent = '∞';

            // Keep exhale state briefly to let animation complete
            setTimeout(() => {
                if (this.isRunning) {
                    this.startNewCycle();
                }
            }, 800);
        } else {
            // Normal phase transitions: inhale -> hold, hold -> exhale
            const phaseTransitions = {
                'inhale': 'hold',
                'hold': 'exhale'
            };

            const nextPhase = phaseTransitions[this.currentPhase];
            this.enterPhase(nextPhase);
        }
    }
    
    updatePhaseDisplay(phase) {
        const config = this.phaseConfig[phase];
        
        if (this.elements.phaseSymbol) {
            this.elements.phaseSymbol.textContent = config.symbol;
            this.elements.phaseSymbol.style.color = config.color;
        }
        
        if (this.elements.phaseText) {
            this.elements.phaseText.textContent = config.text;
            this.elements.phaseText.style.color = config.color;
        }
    }
    
    updateMoonPortal(phase) {
        if (!this.elements.moonPortal) return;
        
        // Remove all existing moon classes
        this.elements.moonPortal.className = 'moon-portal';
        
        // Add the appropriate phase class
        const config = this.phaseConfig[phase];
        if (config.moonClass) {
            this.elements.moonPortal.classList.add(config.moonClass);
        }
    }
    
    updateCosmicContainer(phase) {
        this.elements.appContainer.className = `app-container cosmic-phase-${phase}`;
        this.updateCosmicWavePhase(phase);
    }

    updateCosmicWavePhase(phase) {
        if (!this.elements.cosmicWaves) return;

        const waves = this.elements.cosmicWaves.querySelectorAll('.cosmic-wave');
        waves.forEach(wave => {
            // Remove all phase classes
            wave.classList.remove('phase-inhale', 'phase-hold', 'phase-exhale');

            // Add current phase class
            if (phase !== 'ready') {
                wave.classList.add(`phase-${phase}`);
            }
        });
    }
    
    activateCosmicEnergies() {
        // Activate cosmic wave visualizations
        if (this.elements.cosmicWaves) {
            const waves = this.elements.cosmicWaves.querySelectorAll('.cosmic-wave');
            waves.forEach(wave => {
                wave.classList.add('active');
            });
            
            this.startCosmicWaveIntensityControl(waves);
        }
        
        // Activate energy field
        if (this.elements.energyField) {
            this.elements.energyField.classList.add('active');
        }
    }
    
    deactivateCosmicEnergies() {
        // Deactivate all cosmic effects
        if (this.elements.cosmicWaves) {
            const waves = this.elements.cosmicWaves.querySelectorAll('.cosmic-wave');
            waves.forEach(wave => {
                wave.classList.remove('active');
            });
        }
        
        if (this.elements.energyField) {
            this.elements.energyField.classList.remove('active');
        }
    }
    
    startCosmicWaveIntensityControl(waves) {
        this.waveIntervalId = setInterval(() => {
            if (!this.isRunning) return;
            
            const intensity = this.calculateCosmicIntensity();
            this.adjustWaveIntensity(waves, intensity);
            
        }, 1000); // Update cosmic waves periodically
    }
    
    calculateCosmicIntensity() {
        switch (this.currentPhase) {
            case 'inhale':
                // Gentle intensity during inhale
                return 0.6;

            case 'hold':
                // Steady intensity during hold
                return 0.8;

            case 'exhale':
                // Higher intensity during exhale
                return 1.0;

            default:
                return 0.3;
        }
    }
    
    adjustWaveIntensity(waves, intensity) {
        waves.forEach((wave, index) => {
            const waveOpacity = intensity * (0.7 - index * 0.08);

            wave.style.opacity = Math.max(0.1, waveOpacity);

            // Let CSS animations handle the scaling and timing
            // Only adjust opacity based on intensity
        });
    }
    
    updateToggleButton() {
        if (!this.elements.toggleBtn) return;
        
        if (this.isRunning) {
            // Change to 'Return to Void' state
            this.elements.toggleBtn.classList.add('active');
            this.elements.btnSymbol.textContent = '◼';
            this.elements.btnText.textContent = 'Return to Void';
            this.elements.btnRunes.textContent = 'ᚱᛖᛏᚢᚱᚾ';
        } else {
            // Change to 'Begin Journey' state
            this.elements.toggleBtn.classList.remove('active');
            this.elements.btnSymbol.textContent = '◯';
            this.elements.btnText.textContent = 'Begin Journey';
            this.elements.btnRunes.textContent = 'ᚨᚹᚨᚴᛖᚾ';
        }
    }
}

/**
 * ═══════════════════════════════════════════════════════════════════════════════════
 * COSMIC INITIALIZATION
 * ═══════════════════════════════════════════════════════════════════════════════════
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌌 Initializing Sacred Breath - Mystical Experience...');
    
    const app = new MysticalBreathingApp();
    
    // Cosmic loading animation
    setTimeout(() => {
        document.body.style.opacity = '1';
        console.log('✨ Mystical interface materialized');
    }, 200);
    
    // Handle cosmic window events
    window.addEventListener('resize', () => {
        // Adjust cosmic proportions if needed
    });
    
    // Cosmic visibility handling
    document.addEventListener('visibilitychange', () => {
        if (document.hidden && app.isRunning) {
            console.log('🌑 Cosmic journey paused - tab hidden');
            // Optionally pause the journey
        }
    });
    
    // Expose mystical controls to console for advanced practitioners
    window.MysticalBreath = {
        app: app,
        beginJourney: () => app.beginJourney(),
        returnToVoid: () => app.returnToVoid(),
        getCurrentPhase: () => app.currentPhase,
        getCurrentCycle: () => app.currentCycle,
        setCustomDurations: (inhale, hold, exhale) => {
            app.phaseDurations = { inhale, hold, exhale };
            console.log(`🔮 Custom durations set: ${inhale}-${hold}-${exhale}`);
        }
    };
    
    console.log('🧙‍♂️ Sacred Breath initialized successfully');
    console.log('📚 Access advanced controls via: window.MysticalBreath');
    console.log('⌨️  Space = Start/Stop | Escape = Stop | Mobile = Swipe gestures');
});
