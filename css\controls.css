/* ═══════════════════════════════════════════════════════════════════════════════════
   SACRED BREATH - CONTROL STYLES
   Mystical controls, buttons, and wisdom panel
   ═══════════════════════════════════════════════════════════════════════════════════ */

/* ═══ MYSTICAL CONTROLS ═══ */
.mystical-controls {
    display: flex;
    gap: 20px;
    z-index: 10;
    align-items: center;
    justify-content: center;
    cursor: default;
    flex-shrink: 0;
    width: 100%;
}

.mystical-btn {
    position: relative;
    width: 150px;
    height: 150px;
    padding: 0;
    background: transparent;
    border: 4px solid var(--mystical-violet);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: var(--font-ethereal);
    backdrop-filter: blur(15px);
    box-shadow:
        0 0 40px rgba(139, 92, 246, 0.5),
        inset 0 0 30px rgba(139, 92, 246, 0.2);
    overflow: hidden;
}

.mystical-btn::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: conic-gradient(transparent,
            var(--mystical-violet),
            transparent);
    border-radius: 50%;
    animation: btnRotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.mystical-btn:hover:not(:disabled)::before {
    opacity: 0.5;
}

.mystical-btn:active {
    transform: translateY(-2px) scale(0.95);
    transition: all 0.1s ease;
}

.mystical-btn .btn-symbol:hover {
    transform: scale(1.1);
    text-shadow: 0 0 30px currentColor;
}

/* Active state symbol animation */
.btn-toggle.active .btn-symbol {
    animation: symbolPulse 2s ease-in-out infinite;
}

.btn-toggle.active .btn-runes {
    animation: runeShimmer 3s ease-in-out infinite;
}

.mystical-btn:hover:not(:disabled) {
    transform: translateY(-5px) scale(1.1);
    box-shadow:
        0 0 60px var(--mystical-violet),
        0 15px 40px rgba(139, 92, 246, 0.5);
    border-color: var(--ethereal-cyan);
}

.btn-toggle {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 40px rgba(16, 185, 129, 0.4),
        inset 0 0 35px rgba(16, 185, 129, 0.15);
    transition: all 0.5s ease;
}

.btn-toggle:hover:not(:disabled) {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 70px var(--aurora-green),
        0 15px 40px rgba(16, 185, 129, 0.5);
}

.btn-toggle.active {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 50px rgba(236, 72, 153, 0.5),
        inset 0 0 35px rgba(236, 72, 153, 0.2);
    animation: activeButtonPulse 2s ease-in-out infinite;
}

.btn-toggle.active::before {
    opacity: 0.4;
    animation: btnRotate 3s linear infinite;
}

.btn-toggle.active:hover {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 80px var(--celestial-pink),
        0 15px 40px rgba(236, 72, 153, 0.6);
    animation: activeButtonPulse 1.5s ease-in-out infinite;
}

.mystical-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.2);
}

.btn-symbol {
    font-size: 3.5rem;
    color: var(--lunar-silver);
    text-shadow: 0 0 25px currentColor;
    z-index: 2;
    position: relative;
    transition: all 0.3s ease;
}

.btn-text {
    display: none;
}

.btn-runes {
    font-size: 0.9rem;
    color: var(--sacred-gold);
    opacity: 0.8;
    letter-spacing: 2px;
    z-index: 2;
    position: relative;
    text-shadow: 0 0 12px var(--sacred-gold);
}

/* Control buttons - Simplified */
.controls {
    display: flex;
    gap: 20px;
    z-index: 10;
}

.btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    min-width: 150px;
    color: white;
}

.btn-start {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-stop {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
}

.btn-start:hover:not(:disabled) {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-stop:hover:not(:disabled) {
    background: linear-gradient(45deg, #d32f2f, #c62828);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Phase-specific button states */
.cosmic-phase-inhale .btn-toggle.active {
    border-color: var(--aurora-green);
    box-shadow:
        0 0 50px rgba(16, 185, 129, 0.6),
        inset 0 0 35px rgba(16, 185, 129, 0.2);
}

.cosmic-phase-inhale .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--aurora-green),
            transparent);
}

.cosmic-phase-hold .btn-toggle.active {
    border-color: var(--sacred-gold);
    box-shadow:
        0 0 60px rgba(251, 191, 36, 0.7),
        inset 0 0 40px rgba(251, 191, 36, 0.25);
}

.cosmic-phase-hold .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--sacred-gold),
            transparent);
}

.cosmic-phase-exhale .btn-toggle.active {
    border-color: var(--celestial-pink);
    box-shadow:
        0 0 55px rgba(236, 72, 153, 0.6),
        inset 0 0 35px rgba(236, 72, 153, 0.2);
}

.cosmic-phase-exhale .btn-toggle.active::before {
    background: conic-gradient(transparent,
            var(--celestial-pink),
            transparent);
}

/* Enhanced active state transitions */
.btn-toggle.active {
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-toggle.active .btn-symbol {
    transition: all 0.5s ease;
}

.btn-toggle.active .btn-runes {
    transition: all 0.5s ease;
}

/* ═══ WISDOM PANEL ═══ */
.wisdom-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 5, 17, 0.9);
    border: 1px solid var(--mystical-violet);
    border-radius: 12px;
    padding: 12px;
    max-width: 280px;
    width: 280px;
    height: auto;
    font-family: var(--font-ethereal);
    backdrop-filter: blur(15px);
    box-shadow:
        0 0 20px rgba(139, 92, 246, 0.3),
        inset 0 0 20px rgba(139, 92, 246, 0.1);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.9;
    cursor: pointer;
    overflow: hidden;
}

.wisdom-panel:hover {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow:
        0 0 30px rgba(139, 92, 246, 0.4),
        inset 0 0 25px rgba(139, 92, 246, 0.15);
}

.wisdom-panel.collapsed:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 0 35px rgba(139, 92, 246, 0.6),
        inset 0 0 20px rgba(139, 92, 246, 0.3);
}

.wisdom-panel.collapsed:hover::before {
    animation-duration: 1.5s;
    text-shadow:
        0 0 20px var(--sacred-gold),
        0 0 40px var(--ethereal-cyan),
        0 0 60px var(--mystical-violet);
}

/* Collapsible wisdom panel */
.wisdom-panel.collapsed {
    width: 60px;
    height: 60px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, rgba(0, 5, 17, 0.95) 70%);
    border: none;
    outline: none;
    box-shadow:
        0 0 25px rgba(139, 92, 246, 0.5),
        inset 0 0 15px rgba(139, 92, 246, 0.2);
    position: fixed;
    bottom: 20px;
    right: 20px;
}

.wisdom-panel.collapsed::before {
    content: '✩';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: var(--sacred-gold);
    text-shadow:
        0 0 15px var(--sacred-gold),
        0 0 25px var(--ethereal-cyan);
    animation: mysticalPulse 3s ease-in-out infinite;
}

.wisdom-panel.collapsed .wisdom-header,
.wisdom-panel.collapsed .wisdom-title,
.wisdom-panel.collapsed .wisdom-content,
.wisdom-panel.collapsed .ancient-symbol {
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease;
}

.wisdom-panel.collapsed .wisdom-content {
    height: 0;
    overflow: hidden;
}

/* Expanded state transitions */
.wisdom-panel:not(.collapsed) .wisdom-title {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.1s;
}

.wisdom-panel:not(.collapsed) .wisdom-content {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.2s;
    height: auto;
}

.wisdom-panel:not(.collapsed) .ancient-symbol:last-child {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease 0.1s;
}

.wisdom-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
}

.ancient-symbol {
    font-size: 1.2rem;
    color: var(--sacred-gold);
    text-shadow: 0 0 8px var(--sacred-gold);
    animation: ancientPulse 6s ease-in-out infinite alternate;
    transition: all 0.3s ease;
}

.wisdom-title {
    font-family: var(--font-mystical);
    font-size: 1.1rem;
    color: var(--ethereal-cyan);
    margin: 0;
    text-align: center;
    letter-spacing: 1px;
}

.wisdom-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    transition: all 0.3s ease;
}

.breath-phase {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 8px;
    background: rgba(139, 92, 246, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.phase-rune {
    font-size: 1.3rem;
    color: var(--mystical-violet);
    text-shadow: 0 0 8px var(--mystical-violet);
    flex-shrink: 0;
    margin-top: 2px;
}

.phase-description {
    flex: 1;
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--lunar-silver);
}

.phase-description strong {
    color: var(--ethereal-cyan);
    font-weight: 600;
}

.phase-description em {
    color: var(--sacred-gold);
    font-style: italic;
    opacity: 0.9;
    font-size: 0.75rem;
}
